<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志转换器</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 引入ExcelJS库 -->
    <script src="https://cdn.jsdelivr.net/npm/exceljs@4.4.0/dist/exceljs.min.js"></script>
    <!-- 性能优化配置 -->
    <script src="performance-config.js"></script>
</head>
<body>
    <!-- 全局文件选择按钮 -->
    <div class="global-file-selector">
        <input type="file" id="fileInput" accept=".xlsx" class="file-input">
        <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
            选择文件
        </button>
    </div>

    <!-- 文件信息显示 -->
    <div class="file-info" id="fileInfo" style="display: none;">
        <div class="file-details">
            <span class="file-name" id="fileName"></span>
            <span class="file-meta">
                <span id="fileSize"></span> • <span id="fileDate"></span>
            </span>
        </div>
        <button id="convertBtn" class="reprocess-btn" style="display: none;">重新处理</button>
    </div>

    <div class="container">
        <main class="main-content">

            <!-- 月视图区域 -->
            <div class="month-view-section" id="monthViewSection" style="display: none;">
                <div class="month-view-header">
                    <div class="stats-summary">
                        <span class="stats-item">
                            <strong id="totalRecords">0</strong> 条记录
                        </span>
                        <span class="stats-item">
                            <span id="dateRange">-</span>
                        </span>
                        <span class="stats-item selected-count">
                            已选择 <strong id="selectedCount">0</strong> 个日期
                        </span>
                    </div>

                    <!-- 月历导航 -->
                    <div class="calendar-navigation">
                        <button class="nav-btn" id="prevMonth">‹</button>
                        <span class="current-month" id="currentMonth">2024年1月</span>
                        <button class="nav-btn" id="nextMonth">›</button>
                        <button class="clear-btn" id="clearSelection">清除</button>
                    </div>
                </div>

                <!-- 月历容器 -->
                <div class="calendar-container" id="calendarContainer">
                    <!-- 月历将通过JavaScript动态生成 -->
                </div>

                <!-- 选中日期和操作区域 -->
                <div class="bottom-section">
                    <div class="selected-dates">
                        <div class="selected-dates-list" id="selectedDatesList">
                            <span class="no-selection">请选择要导出的日期</span>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn preview-btn" id="previewData">预览数据</button>
                        <button class="action-btn export-btn" id="exportFiltered" disabled>导出Excel</button>
                    </div>
                </div>
            </div>

            <!-- 状态消息区域 -->
            <div class="message" id="messageArea"></div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
